package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config 应用配置结构体
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
	AI       AIConfig       `json:"ai"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `json:"port"`
	Mode string `json:"mode"` // debug, release, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	MaxOpen  int    `json:"max_open"`  // 最大打开连接数
	MaxIdle  int    `json:"max_idle"`  // 最大空闲连接数
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Password string `json:"password"`
	DB       int    `json:"db"`
	PoolSize int    `json:"pool_size"` // 连接池大小
}

// AIConfig AI模型配置
type AIConfig struct {
	QwenKey     string `json:"qwen_key"`
	DeepSeekKey string `json:"deepseek_key"`
}

// LoadConfig 加载配置
// 从环境变量或.env文件中读取配置信息
func LoadConfig() (*Config, error) {
	// 尝试加载.env文件，如果不存在则忽略
	_ = godotenv.Load()

	config := &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("MYSQL_HOST", "***********"),
			Port:     getEnv("MYSQL_PORT", "3380"),
			Username: getEnv("MYSQL_USERNAME", "gmdns"),
			Password: getEnv("MYSQL_PASSWORD", "5e7fFn3HpPfuQ6Qx42Az"),
			Database: getEnv("MYSQL_DATABASE", "solve_api_go"),
			MaxOpen:  20, // 默认最大打开连接数
			MaxIdle:  10, // 默认最大空闲连接数
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "************"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", "4HY8xm8dECYmDSeaX8GC"),
			DB:       getEnvInt("REDIS_DB", 0),  // 从环境变量读取DB编号
			PoolSize: 20, // 默认连接池大小
		},
		AI: AIConfig{
			QwenKey:     getEnv("QWEN_KEY", "sk-3920274bedf642c2b7495f534aadca84"),
			DeepSeekKey: getEnv("DEEPSEEK_KEY", "***********************************"),
		},
	}

	// 验证必要的配置项
	if err := validateConfig(config); err != nil {
		return nil, err
	}

	return config, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取环境变量并转换为整数，如果不存在或转换失败则返回默认值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// validateConfig 验证配置的有效性
func validateConfig(config *Config) error {
	// 验证数据库配置
	if config.Database.Host == "" {
		log.Fatal("数据库主机地址不能为空")
	}
	if config.Database.Username == "" {
		log.Fatal("数据库用户名不能为空")
	}
	if config.Database.Database == "" {
		log.Fatal("数据库名称不能为空")
	}

	// 验证Redis配置
	if config.Redis.Host == "" {
		log.Fatal("Redis主机地址不能为空")
	}

	// 验证AI配置
	if config.AI.QwenKey == "" {
		log.Fatal("Qwen API密钥不能为空")
	}
	if config.AI.DeepSeekKey == "" {
		log.Fatal("DeepSeek API密钥不能为空")
	}

	return nil
}

// GetDSN 获取MySQL数据源名称
func (c *DatabaseConfig) GetDSN() string {
	return c.Username + ":" + c.Password + "@tcp(" + c.Host + ":" + c.Port + ")/" + c.Database + "?charset=utf8mb4&parseTime=True&loc=Local"
}

// GetRedisAddr 获取Redis连接地址
func (c *RedisConfig) GetRedisAddr() string {
	return c.Host + ":" + c.Port
}
