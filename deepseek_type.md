本文档仅用于理解deepseek-chat模型的调用方式以及参数配置。更多的结构参数请参考deepseek-chat的官方文档。
禁止移除目前文档要求的字段的配置项，这些是必须参在的且可配置的，如果需要增加其他值，需要与我沟通确认。

下述数据表时qwen已经建立的部分字段差异对其已经在下文实现，部分参数在deepseek中不存在，则为无需调用

表所需字段//支持数据库配置
model_name  =  模型名称  
role_system  =  system角色的Content
role_user  =  user角色的Content 
temperature  =  温度 //默认值为0
top_p  =  TopP  //默认值为0.8 越高文本越多样性
top_k  =  TopK  //默认值为50  候选集越少，稳定稳定性越高
repetition_penalty  =  重复惩罚  //OCR建议为1-1.05
presence_penalty  =  存在惩罚   //OCR建议1.5
response_format  =  返回格式  //json_object或text




按照以下格式请求，要求的参数需要数据库来配置值。



package main

import (
  "fmt"
  "strings"
  "net/http"
  "io/ioutil"
)

func main() {

  url := "https://api.deepseek.com/chat/completions"
  method := "POST"

  payload := strings.NewReader(`{
  "messages": [
    {
      "content": "You are a helpful assistant", //这里由数据库配置读取role_system
      "role": "system"
    },
    {
      "content": "Hi",  //这里由数据库配置读取role_user
      "role": "user"    
    },
    {
      "content": "${qwen_parsed}", //这里是需要将格式化解析后的值也就是计划存入qwen_parsed的值
      "role": "user"    
    }
  ],
  "model": "deepseek-chat",
  "frequency_penalty": 0,   //这里由数据库配置读取repetition_penalty字段
  "presence_penalty": 0,    //这里由数据库配置读取presence_penalty字段
  "response_format": {
    "type": "json_object"   //这里由数据库配置读取response_format字段
  },
  "temperature": 1,         //这里由数据库配置读取temperature字段
  "top_p": 1
}`)

  client := &http.Client {
  }
  req, err := http.NewRequest(method, url, payload)

  if err != nil {
    fmt.Println(err)
    return
  }
  req.Header.Add("Content-Type", "application/json")
  req.Header.Add("Accept", "application/json")
  req.Header.Add("Authorization", "Bearer <TOKEN>")

  res, err := client.Do(req)
  if err != nil {
    fmt.Println(err)
    return
  }
  defer res.Body.Close()

  body, err := ioutil.ReadAll(res.Body)
  if err != nil {
    fmt.Println(err)
    return
  }
  fmt.Println(string(body))
}