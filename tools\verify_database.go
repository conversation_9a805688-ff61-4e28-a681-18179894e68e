package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"solve_api_go/internal/config"
)

func main() {
	fmt.Println("=== 数据库验证工具 ===")
	fmt.Println()

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	dsn := cfg.Database.GetDSN()
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(5)
	db.SetMaxIdleConns(2)

	if err := db.<PERSON>(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")
	fmt.Println()

	// 显示当前数据库中的所有表
	fmt.Println("当前数据库中的表:")
	showAllTables(db)
	fmt.Println()

	// 验证表结构
	fmt.Println("1. 验证 questions 表...")
	verifyQuestionsTable(db)

	fmt.Println()
	fmt.Println("2. 验证 quest_model_config 表...")
	verifyModelConfigTable(db)

	fmt.Println()
	fmt.Println("🎉 数据库验证完成!")
}

func showAllTables(db *sql.DB) {
	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		fmt.Printf("❌ 获取表列表失败: %v\n", err)
		return
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		err := rows.Scan(&tableName)
		if err != nil {
			fmt.Printf("❌ 读取表名失败: %v\n", err)
			return
		}
		tables = append(tables, tableName)
	}

	if len(tables) == 0 {
		fmt.Println("  数据库中没有表")
	} else {
		for _, table := range tables {
			fmt.Printf("  - %s\n", table)
		}
	}
}

func verifyQuestionsTable(db *sql.DB) {
	// 检查表是否存在
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'questions'").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 检查 questions 表失败: %v\n", err)
		return
	}

	if count == 0 {
		fmt.Println("❌ questions 表不存在")
		return
	}

	fmt.Println("✅ questions 表存在")

	// 检查表结构
	rows, err := db.Query("DESCRIBE questions")
	if err != nil {
		fmt.Printf("❌ 获取 questions 表结构失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("  表结构:")
	for rows.Next() {
		var field, fieldType, null, key, defaultValue, extra sql.NullString
		err := rows.Scan(&field, &fieldType, &null, &key, &defaultValue, &extra)
		if err != nil {
			fmt.Printf("❌ 读取表结构失败: %v\n", err)
			return
		}
		fmt.Printf("    %s: %s\n", field.String, fieldType.String)
	}

	// 检查索引
	rows, err = db.Query("SHOW INDEX FROM questions")
	if err != nil {
		fmt.Printf("❌ 获取 questions 表索引失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("  索引:")
	for rows.Next() {
		var table, nonUnique, keyName, seqInIndex, columnName, collation, cardinality, subPart, packed, null, indexType, comment, indexComment, visible, expression sql.NullString
		err := rows.Scan(&table, &nonUnique, &keyName, &seqInIndex, &columnName, &collation, &cardinality, &subPart, &packed, &null, &indexType, &comment, &indexComment, &visible, &expression)
		if err != nil {
			fmt.Printf("❌ 读取索引信息失败: %v\n", err)
			return
		}
		fmt.Printf("    %s (%s)\n", keyName.String, columnName.String)
	}
}

func verifyModelConfigTable(db *sql.DB) {
	// 检查表是否存在
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'quest_model_config'").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 检查 quest_model_config 表失败: %v\n", err)
		return
	}

	if count == 0 {
		fmt.Println("❌ quest_model_config 表不存在")
		return
	}

	fmt.Println("✅ quest_model_config 表存在")

	// 检查默认数据
	err = db.QueryRow("SELECT COUNT(*) FROM quest_model_config").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 检查配置数据失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 配置数据记录数: %d\n", count)

	// 显示配置数据
	rows, err := db.Query("SELECT model_name, role_system FROM quest_model_config")
	if err != nil {
		fmt.Printf("❌ 获取配置数据失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("  配置数据:")
	for rows.Next() {
		var modelName, roleSystem string
		err := rows.Scan(&modelName, &roleSystem)
		if err != nil {
			fmt.Printf("❌ 读取配置数据失败: %v\n", err)
			return
		}
		fmt.Printf("    %s: %s...\n", modelName, roleSystem[:50])
	}
}
