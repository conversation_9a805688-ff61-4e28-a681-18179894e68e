使用golang开发一个api服务。需要使用gin框架。mysql8以及redis。
相关三方的配置key如redis、mysql、qwen密钥等信息已经保存在config.md文档



核心功能，用户携带一个图片url请求API服务，我们API在完成业务处理后给用户返回数据。以下是业务逻辑：
1. 验证图片是否有效可访问。有效执行下面步骤。无效返回图片资源不存在，请重新上传。
1. 将图片url提交给qwen-vl-plus模型，得到qwen的返回数据。（需要将qwen的配置参数处理成动态的，在数据库中配置，动态取用。具体要求查阅qwen_type.md文件）
2. 将qwen的原始数据进行预处理解析（方法名定义：FormatQwenData。具体业务逻辑查看FormatQwenData.md文件）
3. 将解析后的完整数据进行哈希处理，然后生成缓存键（quest:哈希值）的键名。（redis.md中有更详细的解释）
4. redis查询缓存键是否存在
    4.1 redis存在     →   则直返回对应的valve给用户。
    4.2 redis不存在   →   降级mysql查询。    
                4.2.1 mysql存在       →， 回写redis（方法名定义：WriteToRedis。具体业务逻辑查看WriteToRedis.md文件），然后返回对应的valve。
                4.2.2 mysql不存在     →， 降级调用deepseek模型，将预处理解析后的数据。发送给deepseek模型，得到deepseek的返回数据。

5. 将deepseek的返回数据进行拆分入库（方法名定义：SaveDeepseekToDatabase。具体业务逻辑查看SaveDeepseekToDatabase.md文件）

6. 入库后在进行redis的写入。（方法名定义：SaveDeepseekToDatabase。具体业务逻辑查看SaveDeepseekToDatabase.md文件）

7. 最终返回给用户的数据是一个数组。


最终mysql需要保存的除deepseek返回的数据字段外，还需要存入缓存键、用户上传的图片url、qwen返回的原始数据、预处理解析后的qwen的数据、deepseek返回的原始数据、

数据库字段名称如下：
cache_key_hash  = 被哈希化的缓存键名字	
question_type  =  问题类型	
question_text  =  问题内容	
option_a  =  问题选项B	
option_b  =  问题选项C	
option_c  =  问题选项D	
option_d  =  问题选项E	
option_y  =  问题选项Y	
option_n  =  问题选项N	
answer  =  问题答案	
analysis  =  问题解析	
user_image  =  问题对应的图片名称	
image_url  =  用户提交的图片 URL 地址
qwen_raw  =  qwen 返回的原始数据	
deepseek_raw  =  deepseek 返回的原始数据	
qwen_parsed  =  被格式化解析后的 qwen 数据	
is_verified  =  是否已经验证过，默认值为0



方法名介绍
FormatQwenData  =  将 Qwen 返回的数据进行格式化处理
WriteToRedis  =  回写 Redis 的方法	
SaveDeepseekToDatabase  =  拆分 DeepSeek 返回数据并写入数据库	
