# Solve API Go 启动脚本
# PowerShell 脚本用于启动服务

Write-Host "=== Solve API Go 启动脚本 ===" -ForegroundColor Green
Write-Host ""

# 设置Go环境变量
$env:PATH = "D:\studio\solve_api_go\go\bin;" + $env:PATH
$env:GOPROXY = "https://goproxy.cn,direct"

# 切换到项目目录
Set-Location "D:\studio\solve_api_go"

Write-Host "正在检查Go环境..." -ForegroundColor Yellow
try {
    $goVersion = & go version
    Write-Host "Go环境正常: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Go环境未正确配置" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "正在编译项目..." -ForegroundColor Yellow
try {
    & go build cmd/server/main.go
    if ($LASTEXITCODE -eq 0) {
        Write-Host "编译成功!" -ForegroundColor Green
    } else {
        Write-Host "编译失败!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "编译过程中发生错误: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "正在启动服务..." -ForegroundColor Yellow
Write-Host "服务地址: http://localhost:8080" -ForegroundColor Cyan
Write-Host "健康检查: http://localhost:8080/health" -ForegroundColor Cyan
Write-Host "API接口: http://localhost:8080/api/v1/analyze-image" -ForegroundColor Cyan
Write-Host ""
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host ""

# 启动服务
try {
    & .\main.exe
} catch {
    Write-Host "启动服务时发生错误: $_" -ForegroundColor Red
    exit 1
}
