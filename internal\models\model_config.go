package models

import (
	"time"
)

// ModelConfig AI模型配置数据模型
// 对应数据库中的quest_model_config表，存储AI模型的动态配置参数
type ModelConfig struct {
	ID                uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelName         string    `json:"model_name" gorm:"type:varchar(50);not null;uniqueIndex"` // 模型名称，如qwen-vl-plus, deepseek-chat
	RoleSystem        string    `json:"role_system" gorm:"type:text;not null"`                   // system角色的Content
	RoleUser          string    `json:"role_user" gorm:"type:text;not null"`                     // user角色的Content
	Temperature       float64   `json:"temperature" gorm:"type:decimal(3,2);default:0"`          // 温度，默认值为0
	TopP              float64   `json:"top_p" gorm:"type:decimal(3,2);default:0.8"`              // TopP，默认值为0.8，越高文本越多样性
	TopK              int       `json:"top_k" gorm:"type:int;default:50"`                        // TopK，默认值为50，候选集越少，稳定性越高
	RepetitionPenalty float64   `json:"repetition_penalty" gorm:"type:decimal(4,3);default:1"`   // 重复惩罚，OCR建议为1-1.05
	PresencePenalty   float64   `json:"presence_penalty" gorm:"type:decimal(3,2);default:1.5"`   // 存在惩罚，OCR建议1.5
	ResponseFormat    string    `json:"response_format" gorm:"type:varchar(20);default:'json_object'"` // 返回格式，json_object或text
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime"`                        // 创建时间
	UpdatedAt         time.Time `json:"updated_at" gorm:"autoUpdateTime"`                        // 更新时间
}

// TableName 指定表名
func (ModelConfig) TableName() string {
	return "quest_model_config"
}

// QwenConfig Qwen模型配置结构体
// 用于构建Qwen API请求
type QwenConfig struct {
	ModelName         string  `json:"model_name"`
	RoleSystem        string  `json:"role_system"`
	RoleUser          string  `json:"role_user"`
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	TopK              int     `json:"top_k"`
	RepetitionPenalty float64 `json:"repetition_penalty"`
	PresencePenalty   float64 `json:"presence_penalty"`
	ResponseFormat    string  `json:"response_format"`
}

// DeepSeekConfig DeepSeek模型配置结构体
// 用于构建DeepSeek API请求，字段映射到Qwen配置
type DeepSeekConfig struct {
	ModelName         string  `json:"model_name"`
	RoleSystem        string  `json:"role_system"`
	RoleUser          string  `json:"role_user"`
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	FrequencyPenalty  float64 `json:"frequency_penalty"`  // 映射自RepetitionPenalty
	PresencePenalty   float64 `json:"presence_penalty"`
	ResponseFormat    string  `json:"response_format"`
}

// ToQwenConfig 将ModelConfig转换为QwenConfig
func (mc *ModelConfig) ToQwenConfig() *QwenConfig {
	return &QwenConfig{
		ModelName:         mc.ModelName,
		RoleSystem:        mc.RoleSystem,
		RoleUser:          mc.RoleUser,
		Temperature:       mc.Temperature,
		TopP:              mc.TopP,
		TopK:              mc.TopK,
		RepetitionPenalty: mc.RepetitionPenalty,
		PresencePenalty:   mc.PresencePenalty,
		ResponseFormat:    mc.ResponseFormat,
	}
}

// ToDeepSeekConfig 将ModelConfig转换为DeepSeekConfig
// 注意字段映射：RepetitionPenalty -> FrequencyPenalty
func (mc *ModelConfig) ToDeepSeekConfig() *DeepSeekConfig {
	return &DeepSeekConfig{
		ModelName:        mc.ModelName,
		RoleSystem:       mc.RoleSystem,
		RoleUser:         mc.RoleUser,
		Temperature:      mc.Temperature,
		TopP:             mc.TopP,
		FrequencyPenalty: mc.RepetitionPenalty, // 字段映射
		PresencePenalty:  mc.PresencePenalty,
		ResponseFormat:   mc.ResponseFormat,
	}
}

// QwenAPIRequest Qwen API请求结构体
type QwenAPIRequest struct {
	Model      string     `json:"model"`
	Input      QwenInput  `json:"input"`
	Parameters QwenParams `json:"parameters"`
}

// QwenInput Qwen输入结构体
type QwenInput struct {
	Messages []QwenMessage `json:"messages"`
}

// QwenMessage Qwen消息结构体
type QwenMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
	Image   string `json:"image,omitempty"` // 图片URL，仅在user角色时使用
}

// QwenParams Qwen参数结构体
type QwenParams struct {
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	TopK              int     `json:"top_k"`
	RepetitionPenalty float64 `json:"repetition_penalty"`
	PresencePenalty   float64 `json:"presence_penalty"`
	ResponseFormat    string  `json:"response_format"`
}

// DeepSeekAPIRequest DeepSeek API请求结构体
type DeepSeekAPIRequest struct {
	Messages         []DeepSeekMessage `json:"messages"`
	Model            string            `json:"model"`
	FrequencyPenalty float64           `json:"frequency_penalty"`
	PresencePenalty  float64           `json:"presence_penalty"`
	ResponseFormat   ResponseFormat    `json:"response_format"`
	Temperature      float64           `json:"temperature"`
	TopP             float64           `json:"top_p"`
}

// DeepSeekMessage DeepSeek消息结构体
type DeepSeekMessage struct {
	Content string `json:"content"`
	Role    string `json:"role"`
}

// ResponseFormat 响应格式结构体
type ResponseFormat struct {
	Type string `json:"type"`
}

// BuildQwenRequest 构建Qwen API请求
func (qc *QwenConfig) BuildQwenRequest(imageURL string) *QwenAPIRequest {
	return &QwenAPIRequest{
		Model: "qwen-vl-plus",
		Input: QwenInput{
			Messages: []QwenMessage{
				{
					Role:    "system",
					Content: qc.RoleSystem,
				},
				{
					Role:    "user",
					Content: qc.RoleUser,
					Image:   imageURL,
				},
			},
		},
		Parameters: QwenParams{
			Temperature:       qc.Temperature,
			TopP:              qc.TopP,
			TopK:              qc.TopK,
			RepetitionPenalty: qc.RepetitionPenalty,
			PresencePenalty:   qc.PresencePenalty,
			ResponseFormat:    qc.ResponseFormat,
		},
	}
}

// BuildDeepSeekRequest 构建DeepSeek API请求
func (dc *DeepSeekConfig) BuildDeepSeekRequest(qwenParsedData string) *DeepSeekAPIRequest {
	return &DeepSeekAPIRequest{
		Messages: []DeepSeekMessage{
			{
				Content: dc.RoleSystem,
				Role:    "system",
			},
			{
				Content: dc.RoleUser,
				Role:    "user",
			},
			{
				Content: qwenParsedData,
				Role:    "user",
			},
		},
		Model:            "deepseek-chat",
		FrequencyPenalty: dc.FrequencyPenalty,
		PresencePenalty:  dc.PresencePenalty,
		ResponseFormat: ResponseFormat{
			Type: dc.ResponseFormat,
		},
		Temperature: dc.Temperature,
		TopP:        dc.TopP,
	}
}
