package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
)

// GenerateHash 生成字符串的SHA256哈希值
// 用于生成缓存键的哈希部分
func GenerateHash(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// GenerateCacheKey 生成Redis缓存键
// 格式：quest:{hash}
func GenerateCacheKey(data string) string {
	hash := GenerateHash(data)
	return fmt.Sprintf("quest:%s", hash)
}

// GenerateCacheKeyFromHash 从已有哈希值生成缓存键
// 格式：quest:{hash}
func GenerateCacheKeyFromHash(hash string) string {
	return fmt.Sprintf("quest:%s", hash)
}
