package services

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-redis/redis/v8"
	"solve_api_go/internal/config"
	"solve_api_go/internal/models"
)

// CacheService Redis缓存服务
type CacheService struct {
	client *redis.Client
	ctx    context.Context
}

// NewCacheService 创建新的缓存服务实例
func NewCacheService(cfg *config.Config) *CacheService {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.GetRedisAddr(),
		Username: cfg.Redis.Username,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		PoolSize: cfg.Redis.PoolSize,
	})

	return &CacheService{
		client: rdb,
		ctx:    context.Background(),
	}
}

// Get 从Redis获取缓存数据
// 返回题目响应数组，如果不存在则返回nil
func (s *CacheService) Get(cacheKey string) ([]models.QuestionResponse, error) {
	// 从Redis获取数据
	result, err := s.client.Get(s.ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存不存在
			return nil, nil
		}
		return nil, fmt.Errorf("Redis查询失败: %v", err)
	}

	// 解析JSON数据
	var questions []models.QuestionResponse
	if err := json.Unmarshal([]byte(result), &questions); err != nil {
		return nil, fmt.Errorf("解析缓存数据失败: %v", err)
	}

	return questions, nil
}

// Set 将数据存储到Redis
// 永久缓存，无过期时间
func (s *CacheService) Set(cacheKey string, data []models.QuestionResponse) error {
	// 将数据转换为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 存储到Redis，无过期时间（永久缓存）
	err = s.client.Set(s.ctx, cacheKey, jsonData, 0).Err()
	if err != nil {
		return fmt.Errorf("Redis存储失败: %v", err)
	}

	return nil
}

// Exists 检查缓存键是否存在
func (s *CacheService) Exists(cacheKey string) (bool, error) {
	result, err := s.client.Exists(s.ctx, cacheKey).Result()
	if err != nil {
		return false, fmt.Errorf("Redis查询失败: %v", err)
	}
	
	return result > 0, nil
}

// Delete 删除缓存
func (s *CacheService) Delete(cacheKey string) error {
	err := s.client.Del(s.ctx, cacheKey).Err()
	if err != nil {
		return fmt.Errorf("Redis删除失败: %v", err)
	}
	
	return nil
}

// WriteToRedis 回写Redis的方法
// 从MySQL查询数据并写入Redis缓存
func (s *CacheService) WriteToRedis(cacheKey string, questions []models.Question) error {
	// 将Question数组转换为QuestionResponse数组
	var responses []models.QuestionResponse
	for _, question := range questions {
		response, err := question.ToResponse()
		if err != nil {
			return fmt.Errorf("转换题目数据失败: %v", err)
		}
		responses = append(responses, *response)
	}

	// 存储到Redis
	return s.Set(cacheKey, responses)
}

// Ping 测试Redis连接
func (s *CacheService) Ping() error {
	_, err := s.client.Ping(s.ctx).Result()
	if err != nil {
		return fmt.Errorf("Redis连接失败: %v", err)
	}
	return nil
}

// Close 关闭Redis连接
func (s *CacheService) Close() error {
	return s.client.Close()
}

// GetStats 获取Redis连接池统计信息
func (s *CacheService) GetStats() *redis.PoolStats {
	return s.client.PoolStats()
}
