package services

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"solve_api_go/internal/config"
	"solve_api_go/internal/models"
)

// DatabaseService 数据库服务
type DatabaseService struct {
	db *sql.DB
}

// NewDatabaseService 创建新的数据库服务实例
func NewDatabaseService(cfg *config.Config) (*DatabaseService, error) {
	// 连接数据库
	db, err := sql.Open("mysql", cfg.Database.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(cfg.Database.MaxOpen)
	db.SetMaxIdleConns(cfg.Database.MaxIdle)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return &DatabaseService{db: db}, nil
}

// GetModelConfig 根据模型名称获取模型配置
func (s *DatabaseService) GetModelConfig(modelName string) (*models.ModelConfig, error) {
	query := `
		SELECT id, model_name, role_system, role_user, temperature, top_p, top_k, 
		       repetition_penalty, presence_penalty, response_format, created_at, updated_at
		FROM quest_model_config 
		WHERE model_name = ?
	`
	
	var config models.ModelConfig
	err := s.db.QueryRow(query, modelName).Scan(
		&config.ID,
		&config.ModelName,
		&config.RoleSystem,
		&config.RoleUser,
		&config.Temperature,
		&config.TopP,
		&config.TopK,
		&config.RepetitionPenalty,
		&config.PresencePenalty,
		&config.ResponseFormat,
		&config.CreatedAt,
		&config.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("模型配置不存在: %s", modelName)
		}
		return nil, fmt.Errorf("查询模型配置失败: %v", err)
	}
	
	return &config, nil
}

// GetQuestionsByCacheKey 根据缓存键哈希值查询题目
func (s *DatabaseService) GetQuestionsByCacheKey(cacheKeyHash string) ([]models.Question, error) {
	query := `
		SELECT id, cache_key_hash, question_type, question_text, option_a, option_b, 
		       option_c, option_d, option_y, option_n, answer, analysis, user_image, 
		       image_url, qwen_raw, deepseek_raw, qwen_parsed, is_verified, created_at, updated_at
		FROM questions 
		WHERE cache_key_hash = ?
		ORDER BY id ASC
	`
	
	rows, err := s.db.Query(query, cacheKeyHash)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %v", err)
	}
	defer rows.Close()
	
	var questions []models.Question
	for rows.Next() {
		var q models.Question
		err := rows.Scan(
			&q.ID,
			&q.CacheKeyHash,
			&q.QuestionType,
			&q.QuestionText,
			&q.OptionA,
			&q.OptionB,
			&q.OptionC,
			&q.OptionD,
			&q.OptionY,
			&q.OptionN,
			&q.Answer,
			&q.Analysis,
			&q.UserImage,
			&q.ImageURL,
			&q.QwenRaw,
			&q.DeepSeekRaw,
			&q.QwenParsed,
			&q.IsVerified,
			&q.CreatedAt,
			&q.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描题目数据失败: %v", err)
		}
		questions = append(questions, q)
	}
	
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历题目数据失败: %v", err)
	}
	
	return questions, nil
}

// SaveQuestion 保存题目到数据库
func (s *DatabaseService) SaveQuestion(question *models.Question) error {
	query := `
		INSERT INTO questions (
			cache_key_hash, question_type, question_text, option_a, option_b, 
			option_c, option_d, option_y, option_n, answer, analysis, user_image, 
			image_url, qwen_raw, deepseek_raw, qwen_parsed, is_verified
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	result, err := s.db.Exec(query,
		question.CacheKeyHash,
		question.QuestionType,
		question.QuestionText,
		question.OptionA,
		question.OptionB,
		question.OptionC,
		question.OptionD,
		question.OptionY,
		question.OptionN,
		question.Answer,
		question.Analysis,
		question.UserImage,
		question.ImageURL,
		question.QwenRaw,
		question.DeepSeekRaw,
		question.QwenParsed,
		question.IsVerified,
	)
	
	if err != nil {
		return fmt.Errorf("保存题目失败: %v", err)
	}
	
	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取插入ID失败: %v", err)
	}
	
	question.ID = uint(id)
	return nil
}

// Close 关闭数据库连接
func (s *DatabaseService) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// Ping 测试数据库连接
func (s *DatabaseService) Ping() error {
	return s.db.Ping()
}
