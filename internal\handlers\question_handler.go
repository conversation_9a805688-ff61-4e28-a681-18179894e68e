package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"solve_api_go/internal/models"
	"solve_api_go/internal/services"
	"solve_api_go/internal/utils"
)

// QuestionHandler 题目处理器
type QuestionHandler struct {
	imageService    *services.ImageService
	qwenService     *services.QwenService
	deepSeekService *services.DeepSeekService
	formatService   *services.FormatService
	cacheService    *services.CacheService
	dbService       *services.DatabaseService
}

// NewQuestionHandler 创建新的题目处理器实例
func NewQuestionHandler(
	imageService *services.ImageService,
	qwenService *services.QwenService,
	deepSeekService *services.DeepSeekService,
	formatService *services.FormatService,
	cacheService *services.CacheService,
	dbService *services.DatabaseService,
) *QuestionHandler {
	return &QuestionHandler{
		imageService:    imageService,
		qwenService:     qwenService,
		deepSeekService: deepSeekService,
		formatService:   formatService,
		cacheService:    cacheService,
		dbService:       dbService,
	}
}

// AnalyzeImage 分析图片中的题目内容
// 这是核心的API接口，实现完整的业务流程
func (h *QuestionHandler) AnalyzeImage(c *gin.Context) {
	// 1. 解析请求参数
	var req models.ImageAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "请求参数格式错误")
		return
	}

	// 2. 验证图片是否有效可访问
	if err := h.imageService.ValidateImageURL(req.ImageURL); err != nil {
		utils.BadRequestResponse(c, err.Error())
		return
	}

	// 3. 调用Qwen模型识别图片
	qwenRawData, err := h.qwenService.AnalyzeImage(req.ImageURL)
	if err != nil {
		utils.InternalErrorResponse(c, "图片识别失败", err)
		return
	}

	// 4. 格式化Qwen返回的数据
	qwenResp, err := h.formatService.FormatQwenData(qwenRawData)
	if err != nil {
		utils.BadRequestResponse(c, err.Error())
		return
	}

	// 5. 生成缓存键
	hashData := h.formatService.GenerateHashData(qwenResp)
	cacheKeyHash := utils.GenerateHash(hashData)
	cacheKey := utils.GenerateCacheKeyFromHash(cacheKeyHash)

	// 6. 查询Redis缓存
	cachedData, err := h.cacheService.Get(cacheKey)
	if err != nil {
		utils.InternalErrorResponse(c, "缓存查询失败", err)
		return
	}

	// 6.1 Redis存在，直接返回
	if cachedData != nil {
		utils.SuccessResponse(c, cachedData)
		return
	}

	// 6.2 Redis不存在，查询MySQL
	questions, err := h.dbService.GetQuestionsByCacheKey(cacheKeyHash)
	if err != nil {
		utils.InternalErrorResponse(c, "数据库查询失败", err)
		return
	}

	// 6.2.1 MySQL存在，回写Redis并返回
	if len(questions) > 0 {
		if err := h.cacheService.WriteToRedis(cacheKey, questions); err != nil {
			// 回写失败不影响业务，记录错误但继续返回数据
			// 这里可以添加日志记录
		}

		// 转换为响应格式
		var responses []models.QuestionResponse
		for _, question := range questions {
			response, err := question.ToResponse()
			if err != nil {
				utils.InternalErrorResponse(c, "数据转换失败", err)
				return
			}
			responses = append(responses, *response)
		}

		utils.SuccessResponse(c, responses)
		return
	}

	// 6.2.2 MySQL不存在，调用DeepSeek模型
	qwenParsedJSON, err := h.formatService.ConvertToJSON(qwenResp)
	if err != nil {
		utils.InternalErrorResponse(c, "数据序列化失败", err)
		return
	}

	deepSeekRawData, err := h.deepSeekService.AnalyzeQuestion(qwenParsedJSON)
	if err != nil {
		utils.InternalErrorResponse(c, "题目分析失败", err)
		return
	}

	// 解析DeepSeek响应
	deepSeekResp, err := h.deepSeekService.ParseDeepSeekResponse(deepSeekRawData)
	if err != nil {
		utils.InternalErrorResponse(c, "解析分析结果失败", err)
		return
	}

	// 7. 保存到数据库
	savedQuestion, err := h.deepSeekService.SaveDeepseekToDatabase(
		cacheKeyHash,
		req.ImageURL,
		qwenRawData,
		qwenParsedJSON,
		deepSeekRawData,
		qwenResp,
		deepSeekResp,
	)
	if err != nil {
		utils.InternalErrorResponse(c, "保存数据失败", err)
		return
	}

	// 8. 回写Redis
	savedQuestions := []models.Question{*savedQuestion}
	if err := h.cacheService.WriteToRedis(cacheKey, savedQuestions); err != nil {
		// 回写失败不影响业务，记录错误但继续返回数据
		// 这里可以添加日志记录
	}

	// 9. 返回结果
	response, err := savedQuestion.ToResponse()
	if err != nil {
		utils.InternalErrorResponse(c, "数据转换失败", err)
		return
	}

	utils.SuccessResponse(c, []models.QuestionResponse{*response})
}

// HealthCheck 健康检查接口
func (h *QuestionHandler) HealthCheck(c *gin.Context) {
	// 检查数据库连接
	if err := h.dbService.Ping(); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":   "error",
			"message":  "数据库连接失败",
			"database": false,
			"redis":    false,
		})
		return
	}

	// 检查Redis连接
	redisOK := true
	if err := h.cacheService.Ping(); err != nil {
		redisOK = false
	}

	c.JSON(http.StatusOK, gin.H{
		"status":   "ok",
		"message":  "服务运行正常",
		"database": true,
		"redis":    redisOK,
	})
}
