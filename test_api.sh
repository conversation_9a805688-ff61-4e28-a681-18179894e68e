#!/bin/bash

# Solve API Go 测试脚本
# 用于测试API接口的基本功能

BASE_URL="http://localhost:8080"

echo "=== Solve API Go 测试脚本 ==="
echo ""

# 1. 测试服务信息接口
echo "1. 测试服务信息接口..."
curl -s "$BASE_URL/" | jq .
echo ""

# 2. 测试健康检查接口
echo "2. 测试健康检查接口..."
curl -s "$BASE_URL/health" | jq .
echo ""

# 3. 测试图片分析接口（需要有效的图片URL）
echo "3. 测试图片分析接口..."
echo "请确保提供有效的图片URL进行测试"

# 示例请求（需要替换为实际的图片URL）
# curl -X POST "$BASE_URL/api/v1/analyze-image" \
#   -H "Content-Type: application/json" \
#   -d '{"image_url": "https://example.com/question.jpg"}' | jq .

echo ""
echo "=== 测试完成 ==="
echo "注意：图片分析接口需要提供有效的图片URL才能正常测试"
