package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"solve_api_go/internal/config"
)

func main() {
	fmt.Println("=== 数据库初始化工具 ===")
	fmt.Println()

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("数据库服务器: %s:%s\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("目标数据库: %s\n", cfg.Database.Database)
	fmt.Println()

	// 连接到MySQL服务器（不指定数据库）
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/", 
		cfg.Database.Username, 
		cfg.Database.Password, 
		cfg.Database.Host, 
		cfg.Database.Port)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接MySQL服务器失败: %v", err)
	}
	defer db.Close()

	// 设置连接超时
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(1)
	db.SetMaxIdleConns(1)

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("MySQL服务器连接测试失败: %v", err)
	}

	fmt.Println("✅ MySQL服务器连接成功")

	// 创建数据库
	if err := createDatabase(db, cfg.Database.Database); err != nil {
		log.Fatalf("创建数据库失败: %v", err)
	}

	// 切换到目标数据库
	_, err = db.Exec(fmt.Sprintf("USE %s", cfg.Database.Database))
	if err != nil {
		log.Fatalf("切换到数据库失败: %v", err)
	}

	fmt.Printf("✅ 已切换到数据库: %s\n", cfg.Database.Database)

	// 执行迁移脚本
	if err := runMigrations(db); err != nil {
		log.Fatalf("执行迁移脚本失败: %v", err)
	}

	fmt.Println()
	fmt.Println("🎉 数据库初始化完成!")
}

func createDatabase(db *sql.DB, dbName string) error {
	// 检查数据库是否存在
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", dbName).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查数据库是否存在失败: %v", err)
	}

	if count > 0 {
		fmt.Printf("✅ 数据库 '%s' 已存在\n", dbName)
		return nil
	}

	// 创建数据库
	createSQL := fmt.Sprintf("CREATE DATABASE %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName)
	_, err = db.Exec(createSQL)
	if err != nil {
		return fmt.Errorf("创建数据库失败: %v", err)
	}

	fmt.Printf("✅ 数据库 '%s' 创建成功\n", dbName)
	return nil
}

func runMigrations(db *sql.DB) error {
	fmt.Println()
	fmt.Println("开始执行数据库迁移脚本...")

	// 获取迁移文件列表
	migrationDir := "migrations"
	files, err := ioutil.ReadDir(migrationDir)
	if err != nil {
		return fmt.Errorf("读取迁移目录失败: %v", err)
	}

	// 过滤SQL文件并排序
	var sqlFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".sql") {
			sqlFiles = append(sqlFiles, file.Name())
		}
	}

	if len(sqlFiles) == 0 {
		fmt.Println("⚠️  没有找到迁移脚本")
		return nil
	}

	// 执行每个迁移文件
	for _, filename := range sqlFiles {
		fmt.Printf("执行迁移脚本: %s\n", filename)
		
		filePath := filepath.Join(migrationDir, filename)
		content, err := ioutil.ReadFile(filePath)
		if err != nil {
			return fmt.Errorf("读取迁移文件 %s 失败: %v", filename, err)
		}

		// 分割SQL语句（以分号分隔）
		sqlContent := string(content)
		// 移除注释行
		lines := strings.Split(sqlContent, "\n")
		var cleanLines []string
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" && !strings.HasPrefix(line, "--") {
				cleanLines = append(cleanLines, line)
			}
		}
		cleanSQL := strings.Join(cleanLines, " ")

		// 分割SQL语句
		statements := strings.Split(cleanSQL, ";")

		for i, stmt := range statements {
			stmt = strings.TrimSpace(stmt)
			if stmt == "" {
				continue
			}

			fmt.Printf("  执行SQL语句 %d: %s...\n", i+1, stmt[:min(len(stmt), 80)])
			_, err := db.Exec(stmt)
			if err != nil {
				// 检查是否是"表已存在"错误
				if strings.Contains(err.Error(), "already exists") {
					fmt.Printf("  ⚠️  表已存在，跳过创建\n")
					continue
				}
				return fmt.Errorf("执行SQL语句失败: %v\nSQL: %s", err, stmt)
			}
			fmt.Printf("  ✅ SQL语句 %d 执行成功\n", i+1)
		}

		fmt.Printf("  ✅ %s 执行成功\n", filename)
	}

	return nil
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func init() {
	// 确保在正确的目录中运行
	if _, err := os.Stat("migrations"); os.IsNotExist(err) {
		fmt.Println("错误: 请在项目根目录中运行此工具")
		os.Exit(1)
	}
}
