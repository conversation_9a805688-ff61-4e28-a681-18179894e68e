package services

import (
	"fmt"
	"net/http"
	"time"
)

// ImageService 图片验证服务
type ImageService struct {
	client *http.Client
}

// NewImageService 创建新的图片验证服务实例
func NewImageService() *ImageService {
	return &ImageService{
		client: &http.Client{
			Timeout: 10 * time.Second, // 设置10秒超时
		},
	}
}

// ValidateImageURL 验证图片URL是否有效可访问
// 返回true表示图片可访问，false表示不可访问
func (s *ImageService) ValidateImageURL(imageURL string) error {
	// 创建HEAD请求，只获取响应头，不下载图片内容
	req, err := http.NewRequest("HEAD", imageURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置User-Agent，避免某些服务器拒绝请求
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("图片资源不存在，请重新上传")
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片资源不存在，请重新上传")
	}

	// 检查Content-Type是否为图片类型
	contentType := resp.Header.Get("Content-Type")
	if !isImageContentType(contentType) {
		return fmt.Errorf("URL指向的不是有效的图片资源")
	}

	return nil
}

// isImageContentType 检查Content-Type是否为图片类型
func isImageContentType(contentType string) bool {
	imageTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
		"image/svg+xml",
	}

	for _, imageType := range imageTypes {
		if contentType == imageType {
			return true
		}
	}
	return false
}
