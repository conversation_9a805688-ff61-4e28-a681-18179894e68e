-- 创建题目数据表
-- 存储完整的题目信息，包括原始数据、解析结果和缓存键

CREATE TABLE IF NOT EXISTS `questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cache_key_hash` varchar(64) NOT NULL COMMENT '被哈希化的缓存键名字',
  `question_type` varchar(20) NOT NULL COMMENT '问题类型：单选题、多选题、判断题',
  `question_text` text NOT NULL COMMENT '问题内容',
  `option_a` text COMMENT '问题选项A',
  `option_b` text COMMENT '问题选项B',
  `option_c` text COMMENT '问题选项C',
  `option_d` text COMMENT '问题选项D',
  `option_y` text COMMENT '问题选项Y（判断题用）',
  `option_n` text COMMENT '问题选项N（判断题用）',
  `answer` json COMMENT '问题答案（JSON格式）',
  `analysis` text COMMENT '问题解析',
  `user_image` varchar(255) COMMENT '问题对应的图片名称',
  `image_url` text NOT NULL COMMENT '用户提交的图片URL地址',
  `qwen_raw` json COMMENT 'Qwen返回的原始数据',
  `deepseek_raw` json COMMENT 'DeepSeek返回的原始数据',
  `qwen_parsed` json COMMENT '被格式化解析后的Qwen数据',
  `is_verified` tinyint NOT NULL DEFAULT '0' COMMENT '是否已经验证过，默认值为0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cache_key_hash` (`cache_key_hash`) COMMENT '缓存键哈希索引',
  KEY `idx_question_type` (`question_type`) COMMENT '问题类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目数据表';
