package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"solve_api_go/internal/config"
	"solve_api_go/internal/models"
)

// DeepSeekService DeepSeek AI服务
type DeepSeekService struct {
	apiKey    string
	client    *http.Client
	dbService *DatabaseService
}

// NewDeepSeekService 创建新的DeepSeek AI服务实例
func NewDeepSeekService(cfg *config.Config, dbService *DatabaseService) *DeepSeekService {
	return &DeepSeekService{
		apiKey: cfg.AI.DeepSeekKey,
		client: &http.Client{
			Timeout: 60 * time.Second, // 设置60秒超时
		},
		dbService: dbService,
	}
}

// DeepSeekAPIResponse DeepSeek API响应结构体
type DeepSeekAPIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
}

// AnalyzeQuestion 使用DeepSeek模型分析题目
func (s *DeepSeekService) AnalyzeQuestion(qwenParsedData string) (string, error) {
	// 获取DeepSeek模型配置
	modelConfig, err := s.dbService.GetModelConfig("deepseek-chat")
	if err != nil {
		return "", fmt.Errorf("获取DeepSeek模型配置失败: %v", err)
	}

	// 构建请求
	deepSeekConfig := modelConfig.ToDeepSeekConfig()
	request := deepSeekConfig.BuildDeepSeekRequest(qwenParsedData)

	// 序列化请求数据
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "https://api.deepseek.com/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.apiKey)

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	bodyText, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepSeek API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(bodyText))
	}

	// 解析响应
	var apiResp DeepSeekAPIResponse
	if err := json.Unmarshal(bodyText, &apiResp); err != nil {
		return "", fmt.Errorf("解析DeepSeek响应失败: %v", err)
	}

	// 检查响应内容
	if len(apiResp.Choices) == 0 {
		return "", fmt.Errorf("DeepSeek响应中没有内容")
	}

	content := apiResp.Choices[0].Message.Content
	if content == "" {
		return "", fmt.Errorf("DeepSeek响应内容为空")
	}

	return content, nil
}

// ParseDeepSeekResponse 解析DeepSeek响应内容
func (s *DeepSeekService) ParseDeepSeekResponse(content string) (*models.DeepSeekResponse, error) {
	var deepSeekResp models.DeepSeekResponse
	if err := json.Unmarshal([]byte(content), &deepSeekResp); err != nil {
		return nil, fmt.Errorf("解析DeepSeek响应内容失败: %v", err)
	}
	return &deepSeekResp, nil
}

// SaveDeepseekToDatabase 拆分DeepSeek返回数据并写入数据库
// 这是核心的数据保存方法，实现完整的业务逻辑
func (s *DeepSeekService) SaveDeepseekToDatabase(
	cacheKeyHash string,
	imageURL string,
	qwenRaw string,
	qwenParsed string,
	deepSeekRaw string,
	qwenResp *models.QwenResponse,
	deepSeekResp *models.DeepSeekResponse,
) (*models.Question, error) {
	
	// 创建Question对象
	question := &models.Question{
		CacheKeyHash: cacheKeyHash,
		ImageURL:     imageURL,
		QwenRaw:      qwenRaw,
		DeepSeekRaw:  deepSeekRaw,
		QwenParsed:   qwenParsed,
		IsVerified:   0, // 默认值
	}

	// 设置Qwen数据
	question.SetOptionsFromQwen(qwenResp)

	// 设置DeepSeek数据
	if err := question.SetAnswerFromDeepSeek(deepSeekResp); err != nil {
		return nil, fmt.Errorf("设置DeepSeek数据失败: %v", err)
	}

	// 保存到数据库
	if err := s.dbService.SaveQuestion(question); err != nil {
		return nil, fmt.Errorf("保存题目到数据库失败: %v", err)
	}

	return question, nil
}
