package models

import (
	"encoding/json"
	"time"
)

// Question 题目数据模型
// 对应数据库中的questions表，存储完整的题目信息
type Question struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	CacheKeyHash string    `json:"cache_key_hash" gorm:"type:varchar(64);not null;index"` // 被哈希化的缓存键名字
	QuestionType string    `json:"question_type" gorm:"type:varchar(20);not null"`        // 问题类型：单选题、多选题、判断题
	QuestionText string    `json:"question_text" gorm:"type:text;not null"`               // 问题内容
	OptionA      string    `json:"option_a" gorm:"type:text"`                             // 问题选项A
	OptionB      string    `json:"option_b" gorm:"type:text"`                             // 问题选项B
	OptionC      string    `json:"option_c" gorm:"type:text"`                             // 问题选项C
	OptionD      string    `json:"option_d" gorm:"type:text"`                             // 问题选项D
	OptionY      string    `json:"option_y" gorm:"type:text"`                             // 问题选项Y（判断题用）
	OptionN      string    `json:"option_n" gorm:"type:text"`                             // 问题选项N（判断题用）
	Answer       string    `json:"answer" gorm:"type:json"`                               // 问题答案（JSON格式）
	Analysis     string    `json:"analysis" gorm:"type:text"`                             // 问题解析
	UserImage    string    `json:"user_image" gorm:"type:varchar(255)"`                   // 问题对应的图片名称
	ImageURL     string    `json:"image_url" gorm:"type:text;not null"`                   // 用户提交的图片URL地址
	QwenRaw      string    `json:"qwen_raw" gorm:"type:json"`                             // Qwen返回的原始数据
	DeepSeekRaw  string    `json:"deepseek_raw" gorm:"type:json"`                         // DeepSeek返回的原始数据
	QwenParsed   string    `json:"qwen_parsed" gorm:"type:json"`                          // 被格式化解析后的Qwen数据
	IsVerified   int       `json:"is_verified" gorm:"type:tinyint;default:0"`             // 是否已经验证过，默认值为0
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`                      // 创建时间
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`                      // 更新时间
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// QwenResponse Qwen模型响应结构体
type QwenResponse struct {
	QuestionType string `json:"question_type"` // 题目类型
	QuestionText string `json:"question_text"` // 题目内容
	A            string `json:"A,omitempty"`   // 选项A
	B            string `json:"B,omitempty"`   // 选项B
	C            string `json:"C,omitempty"`   // 选项C
	D            string `json:"D,omitempty"`   // 选项D
	Y            string `json:"Y,omitempty"`   // 选项Y（判断题）
	N            string `json:"N,omitempty"`   // 选项N（判断题）
}

// DeepSeekResponse DeepSeek模型响应结构体
type DeepSeekResponse struct {
	QuestionType string                 `json:"question_type"` // 题目类型
	QuestionText string                 `json:"question_text"` // 题目内容
	Options      map[string]string      `json:"options"`       // 选项
	Answer       map[string]string      `json:"answer"`        // 答案
	Analysis     string                 `json:"analysis"`      // 解析
}

// QuestionResponse 返回给用户的题目响应结构体
type QuestionResponse struct {
	QuestionType string            `json:"question_type"` // 题目类型
	QuestionText string            `json:"question_text"` // 题目内容
	Options      map[string]string `json:"options"`       // 选项
	Answer       map[string]string `json:"answer"`        // 答案
	Analysis     string            `json:"analysis"`      // 解析
}

// ToResponse 将Question转换为QuestionResponse
// 用于返回给用户，过滤敏感信息
func (q *Question) ToResponse() (*QuestionResponse, error) {
	response := &QuestionResponse{
		QuestionType: q.QuestionType,
		QuestionText: q.QuestionText,
		Options:      make(map[string]string),
		Analysis:     q.Analysis,
	}

	// 构建选项映射
	if q.OptionA != "" {
		response.Options["A"] = q.OptionA
	}
	if q.OptionB != "" {
		response.Options["B"] = q.OptionB
	}
	if q.OptionC != "" {
		response.Options["C"] = q.OptionC
	}
	if q.OptionD != "" {
		response.Options["D"] = q.OptionD
	}
	if q.OptionY != "" {
		response.Options["Y"] = q.OptionY
	}
	if q.OptionN != "" {
		response.Options["N"] = q.OptionN
	}

	// 解析答案JSON
	if q.Answer != "" {
		var answerMap map[string]string
		if err := json.Unmarshal([]byte(q.Answer), &answerMap); err != nil {
			return nil, err
		}
		response.Answer = answerMap
	}

	return response, nil
}

// SetOptionsFromQwen 从Qwen响应设置选项
func (q *Question) SetOptionsFromQwen(qwenResp *QwenResponse) {
	q.QuestionType = qwenResp.QuestionType
	q.QuestionText = qwenResp.QuestionText
	q.OptionA = qwenResp.A
	q.OptionB = qwenResp.B
	q.OptionC = qwenResp.C
	q.OptionD = qwenResp.D
	q.OptionY = qwenResp.Y
	q.OptionN = qwenResp.N
}

// SetAnswerFromDeepSeek 从DeepSeek响应设置答案和解析
func (q *Question) SetAnswerFromDeepSeek(deepSeekResp *DeepSeekResponse) error {
	// 将答案转换为JSON字符串存储
	answerJSON, err := json.Marshal(deepSeekResp.Answer)
	if err != nil {
		return err
	}
	
	q.Answer = string(answerJSON)
	q.Analysis = deepSeekResp.Analysis
	
	return nil
}

// ImageAnalysisRequest 图片分析请求结构体
type ImageAnalysisRequest struct {
	ImageURL string `json:"image_url" binding:"required"` // 图片URL地址
}

// ImageAnalysisResponse 图片分析响应结构体
type ImageAnalysisResponse struct {
	Success bool                `json:"success"`         // 请求是否成功
	Message string              `json:"message"`         // 响应消息
	Data    []QuestionResponse  `json:"data,omitempty"`  // 题目数据
	Error   string              `json:"error,omitempty"` // 错误信息
}
