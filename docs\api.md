# Solve API Go - API文档

## 概述

Solve API Go 是一个基于Go语言开发的图片题目解析API服务。通过集成Qwen-VL-Plus和DeepSeek-Chat AI模型，实现图片题目的智能识别和解析。

## 基础信息

- **基础URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 接口列表

### 1. 健康检查

检查服务运行状态和依赖服务连接状态。

**请求信息**
- **URL**: `/health`
- **方法**: `GET`
- **参数**: 无

**响应示例**

成功响应 (200):
```json
{
  "status": "ok",
  "message": "服务运行正常",
  "database": true,
  "redis": true
}
```

服务异常响应 (503):
```json
{
  "status": "error",
  "message": "数据库连接失败",
  "database": false,
  "redis": false
}
```

### 2. 图片题目分析

分析图片中的题目内容，返回题目信息、选项、答案和解析。

**请求信息**
- **URL**: `/api/v1/analyze-image`
- **方法**: `POST`
- **Content-Type**: `application/json`

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image_url | string | 是 | 图片URL地址，必须是可访问的有效图片链接 |

**请求示例**
```json
{
  "image_url": "https://example.com/question-image.jpg"
}
```

**响应格式**

成功响应 (200):
```json
{
  "success": true,
  "message": "图片分析成功",
  "data": [
    {
      "question_type": "多选题",
      "question_text": "雾天跟车行驶,应如何安全驾驶?",
      "options": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物",
        "D": "按喇叭提示行车位置"
      },
      "answer": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物"
      },
      "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。"
    }
  ]
}
```

判断题响应示例:
```json
{
  "success": true,
  "message": "图片分析成功",
  "data": [
    {
      "question_type": "判断题",
      "question_text": "驾驶机动车在雾天应使用远光灯提高能见度。",
      "options": {
        "Y": "正确",
        "N": "错误"
      },
      "answer": {
        "N": "错误"
      },
      "analysis": "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯，因此本题错误。"
    }
  ]
}
```

**错误响应**

请求参数错误 (400):
```json
{
  "success": false,
  "message": "请求参数格式错误",
  "error": "Key: 'ImageAnalysisRequest.ImageURL' Error:Field validation for 'ImageURL' failed on the 'required' tag"
}
```

图片资源不存在 (400):
```json
{
  "success": false,
  "message": "图片资源不存在，请重新上传"
}
```

图片解析异常 (400):
```json
{
  "success": false,
  "message": "图片解析异常，请重新拍摄"
}
```

服务器内部错误 (500):
```json
{
  "success": false,
  "message": "图片识别失败",
  "error": "具体错误信息"
}
```

### 3. 服务信息

获取服务基本信息和可用接口列表。

**请求信息**
- **URL**: `/`
- **方法**: `GET`
- **参数**: 无

**响应示例**
```json
{
  "service": "Solve API Go",
  "version": "1.0.0",
  "description": "图片题目解析API服务",
  "endpoints": {
    "health": "/health",
    "analyze": "/api/v1/analyze-image"
  }
}
```

## 业务流程

1. **图片验证**: 验证提交的图片URL是否可访问
2. **图片识别**: 使用Qwen-VL-Plus模型识别图片中的题目内容
3. **数据预处理**: 格式化和清洗识别结果
4. **缓存查询**: 基于内容哈希查询Redis缓存
5. **数据库查询**: 如果缓存不存在，查询MySQL数据库
6. **AI分析**: 如果数据库也不存在，使用DeepSeek-Chat模型分析题目
7. **数据存储**: 将分析结果保存到数据库和缓存
8. **返回结果**: 返回格式化的题目信息

## 支持的题目类型

- **单选题**: 包含A、B、C、D选项，答案为其中一个选项
- **多选题**: 包含A、B、C、D选项，答案为多个选项的组合
- **判断题**: 包含Y（正确）、N（错误）选项，答案为其中一个

## 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误或业务逻辑错误 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用（依赖服务异常） |

## 注意事项

1. **图片要求**: 
   - 图片URL必须可公开访问
   - 支持常见图片格式（JPG、PNG、GIF等）
   - 图片内容应包含清晰的题目文字

2. **缓存机制**:
   - 相同内容的题目会被永久缓存
   - 缓存基于题目完整内容的哈希值生成

3. **性能优化**:
   - 首次分析需要调用AI模型，响应时间较长（30-60秒）
   - 后续相同题目直接返回缓存结果，响应时间极短

4. **错误处理**:
   - AI模型返回的错误信息会直接透传给客户端
   - 网络异常或服务不可用时会返回相应的错误信息
