package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	_ "github.com/go-sql-driver/mysql"
	"solve_api_go/internal/config"
)

func main() {
	fmt.Println("=== 数据库和缓存连接测试工具 ===")
	fmt.Println()

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 测试MySQL连接
	fmt.Println("1. 测试MySQL连接...")
	testMySQL(cfg)

	fmt.Println()

	// 测试Redis连接
	fmt.Println("2. 测试Redis连接...")
	testRedis(cfg)

	fmt.Println()
	fmt.Println("=== 连接测试完成 ===")
}

func testMySQL(cfg *config.Config) {
	fmt.Printf("连接地址: %s:%s\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("数据库名: %s\n", cfg.Database.Database)
	fmt.Printf("用户名: %s\n", cfg.Database.Username)

	// 连接数据库
	dsn := cfg.Database.GetDSN()
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer db.Close()

	// 设置连接池
	db.SetMaxOpenConns(5)
	db.SetMaxIdleConns(2)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		fmt.Printf("❌ 连接测试失败: %v\n", err)
		return
	}

	fmt.Println("✅ MySQL连接成功!")

	// 测试查询
	var version string
	err = db.QueryRowContext(ctx, "SELECT VERSION()").Scan(&version)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}

	fmt.Printf("✅ MySQL版本: %s\n", version)

	// 检查数据库是否存在
	var dbExists int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", cfg.Database.Database).Scan(&dbExists)
	if err != nil {
		fmt.Printf("❌ 检查数据库失败: %v\n", err)
		return
	}

	if dbExists > 0 {
		fmt.Printf("✅ 数据库 '%s' 存在\n", cfg.Database.Database)
	} else {
		fmt.Printf("⚠️  数据库 '%s' 不存在，需要创建\n", cfg.Database.Database)
	}
}

func testRedis(cfg *config.Config) {
	fmt.Printf("连接地址: %s:%s\n", cfg.Redis.Host, cfg.Redis.Port)
	fmt.Printf("数据库: %d\n", cfg.Redis.DB)

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.GetRedisAddr(),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		PoolSize: 5,
	})
	defer rdb.Close()

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		return
	}

	fmt.Printf("✅ Redis连接成功: %s\n", pong)

	// 测试基本操作
	testKey := "test:connection"
	err = rdb.Set(ctx, testKey, "test_value", time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ Redis写入测试失败: %v\n", err)
		return
	}

	val, err := rdb.Get(ctx, testKey).Result()
	if err != nil {
		fmt.Printf("❌ Redis读取测试失败: %v\n", err)
		return
	}

	if val == "test_value" {
		fmt.Println("✅ Redis读写测试成功")
	} else {
		fmt.Printf("❌ Redis读写测试失败: 期望 'test_value', 得到 '%s'\n", val)
	}

	// 清理测试数据
	rdb.Del(ctx, testKey)

	// 获取Redis信息
	info, err := rdb.Info(ctx, "server").Result()
	if err == nil {
		fmt.Println("✅ Redis服务器信息获取成功")
	}
	_ = info // 避免未使用变量警告
}
