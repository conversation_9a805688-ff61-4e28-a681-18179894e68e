# Solve API Go - 图片题目解析API服务

## 项目概述

基于Go语言开发的图片题目解析API服务，使用Gin框架、MySQL8和Redis构建。通过集成Qwen-VL-Plus和DeepSeek-Chat AI模型，实现图片题目的智能识别和解析。

## 技术架构

- **Web框架**: Gin
- **数据库**: MySQL 8
- **缓存**: Redis
- **AI模型**: 
  - Qwen-VL-Plus (图片识别)
  - DeepSeek-Chat (题目分析)

## 核心功能

1. **图片验证**: 验证用户提交的图片URL是否可访问
2. **图片识别**: 使用Qwen-VL-Plus模型识别图片中的题目内容
3. **数据预处理**: 格式化和清洗Qwen返回的数据
4. **智能缓存**: 基于内容哈希的Redis缓存机制
5. **题目分析**: 使用DeepSeek-Chat模型生成答案和解析
6. **数据持久化**: MySQL存储完整的题目和分析数据

## 业务流程

```
用户图片URL → 图片验证 → Qwen识别 → 数据预处理 → 生成缓存键 
    ↓
Redis查询 → 存在：直接返回 
    ↓
MySQL查询 → 存在：回写Redis并返回
    ↓  
DeepSeek分析 → 数据入库 → 回写Redis → 返回结果
```

## 项目结构

```
solve_api_go/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── internal/
│   ├── config/
│   │   └── config.go            # 配置管理
│   ├── models/
│   │   ├── question.go          # 题目数据模型
│   │   └── model_config.go      # AI模型配置模型
│   ├── services/
│   │   ├── image_service.go     # 图片验证服务
│   │   ├── qwen_service.go      # Qwen AI服务
│   │   ├── deepseek_service.go  # DeepSeek AI服务
│   │   ├── format_service.go    # 数据格式化服务
│   │   ├── cache_service.go     # Redis缓存服务
│   │   └── database_service.go  # 数据库服务
│   ├── handlers/
│   │   └── question_handler.go  # HTTP请求处理
│   ├── middleware/
│   │   └── cors.go              # 跨域中间件
│   └── utils/
│       ├── hash.go              # 哈希工具
│       └── response.go          # 响应格式化
├── migrations/
│   ├── 001_create_questions_table.sql
│   └── 002_create_model_config_table.sql
├── docs/
│   └── api.md                   # API文档
└── README.md                    # 项目文档
```

## 开发进度

### ✅ 已完成
- [x] 项目结构设计
- [x] 技术方案确认
- [x] 业务流程梳理
- [x] 基础项目结构创建
- [x] 配置管理模块 (`internal/config/config.go`)
- [x] 数据模型定义 (`internal/models/`)
- [x] 数据库迁移脚本 (`migrations/`)
- [x] 工具函数模块 (`internal/utils/`)
- [x] 图片验证服务 (`internal/services/image_service.go`)
- [x] 数据格式化服务 (`internal/services/format_service.go`)
- [x] 缓存服务实现 (`internal/services/cache_service.go`)

### ✅ 最新完成
- [x] 数据库服务实现 (`internal/services/database_service.go`)
- [x] Qwen AI集成 (`internal/services/qwen_service.go`)
- [x] DeepSeek AI集成 (`internal/services/deepseek_service.go`)
- [x] HTTP API接口 (`internal/handlers/question_handler.go`)
- [x] 主应用入口 (`cmd/server/main.go`)
- [x] 中间件实现 (`internal/middleware/cors.go`)
- [x] API文档 (`docs/api.md`)
- [x] 环境配置示例 (`.env.example`)

### 🚧 进行中
- [ ] 项目测试和调试

### 📋 待开发
- [ ] 单元测试编写
- [ ] 性能优化
- [ ] 部署文档

## 数据库设计

### 主数据表 (questions)
存储题目的完整信息，包括原始数据、解析结果和缓存键。

### 模型配置表 (quest_model_config)
存储AI模型的动态配置参数，支持Qwen和DeepSeek模型的参数配置。

## API接口

### POST /api/v1/analyze-image
分析图片中的题目内容

**请求参数:**
```json
{
  "image_url": "https://example.com/image.jpg"
}
```

**响应格式:**
```json
[
  {
    "question_type": "多选题",
    "question_text": "题目内容",
    "options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C",
      "D": "选项D"
    },
    "answer": {
      "A": "选项A",
      "B": "选项B"
    },
    "analysis": "题目解析内容"
  }
]
```

## 技术实现要点

### 缓存策略
- 基于格式化后的完整题目内容生成哈希值
- 使用 `quest:{hash}` 格式的缓存键
- Redis永久缓存，无过期时间

### AI模型集成
- Qwen-VL-Plus: 图片识别，支持数据库配置参数
- DeepSeek-Chat: 题目分析，字段映射处理

### 数据处理
- 题目内容清洗（正则表达式处理题号前缀）
- JSON数据格式化和验证
- 错误数据的异常处理

## 快速开始

### 1. 环境要求
- Go 1.19+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 数据库初始化
执行数据库迁移脚本：
```sql
-- 执行 migrations/001_create_questions_table.sql
-- 执行 migrations/002_create_model_config_table.sql
```

### 4. 配置环境变量
复制环境配置文件并修改：
```bash
cp .env.example .env
# 根据实际情况修改 .env 文件中的配置
```

### 5. 启动服务
```bash
go run cmd/server/main.go
```

### 6. 测试接口
```bash
# 健康检查
curl http://localhost:8080/health

# 图片分析
curl -X POST http://localhost:8080/api/v1/analyze-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "https://example.com/question.jpg"}'
```

## 部署说明

### 生产环境配置
项目使用环境变量进行配置，主要包括：
- 数据库连接信息
- Redis连接信息
- AI模型API密钥

## 开发规范

### 代码注释
- 所有公共函数必须添加详细注释
- 复杂业务逻辑需要行内注释说明
- 结构体和接口需要文档注释

### 错误处理
- 统一的错误响应格式
- 详细的错误日志记录（开发阶段）
- AI模型错误信息透传

### 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证完整流程
- 性能测试确保高并发支持
