package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"solve_api_go/internal/models"
)

// SuccessResponse 成功响应
func SuccessResponse(c *gin.Context, data []models.QuestionResponse) {
	c.JSON(http.StatusOK, models.ImageAnalysisResponse{
		Success: true,
		Message: "图片分析成功",
		Data:    data,
	})
}

// ErrorResponse 错误响应
func ErrorResponse(c *gin.Context, statusCode int, message string, err error) {
	response := models.ImageAnalysisResponse{
		Success: false,
		Message: message,
	}
	
	if err != nil {
		response.Error = err.Error()
	}
	
	c.<PERSON>SO<PERSON>(statusCode, response)
}

// BadRequestResponse 400错误响应
func BadRequestResponse(c *gin.Context, message string) {
	ErrorResponse(c, http.StatusBadRequest, message, nil)
}

// InternalErrorResponse 500错误响应
func InternalErrorResponse(c *gin.Context, message string, err error) {
	ErrorResponse(c, http.StatusInternalServerError, message, err)
}

// NotFoundResponse 404错误响应
func NotFoundResponse(c *gin.Context, message string) {
	ErrorResponse(c, http.StatusNotFound, message, nil)
}
