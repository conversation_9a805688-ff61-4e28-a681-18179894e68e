package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"solve_api_go/internal/config"
	"solve_api_go/internal/handlers"
	"solve_api_go/internal/middleware"
	"solve_api_go/internal/services"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化数据库服务
	dbService, err := services.NewDatabaseService(cfg)
	if err != nil {
		log.Fatalf("初始化数据库服务失败: %v", err)
	}
	defer dbService.Close()

	// 初始化缓存服务
	cacheService := services.NewCacheService(cfg)
	defer cacheService.Close()

	// 初始化其他服务
	imageService := services.NewImageService()
	qwenService := services.NewQwenService(cfg, dbService)
	deepSeekService := services.NewDeepSeekService(cfg, dbService)
	formatService := services.NewFormatService()

	// 初始化处理器
	questionHandler := handlers.NewQuestionHandler(
		imageService,
		qwenService,
		deepSeekService,
		formatService,
		cacheService,
		dbService,
	)

	// 创建Gin引擎
	r := gin.Default()

	// 添加中间件
	r.Use(middleware.CORS())

	// 添加路由
	setupRoutes(r, questionHandler)

	// 启动服务器
	addr := ":" + cfg.Server.Port
	log.Printf("服务器启动在端口 %s", cfg.Server.Port)
	log.Printf("健康检查: http://localhost%s/health", addr)
	log.Printf("API文档: http://localhost%s/api/v1/analyze-image", addr)

	if err := r.Run(addr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// setupRoutes 设置路由
func setupRoutes(r *gin.Engine, questionHandler *handlers.QuestionHandler) {
	// 健康检查
	r.GET("/health", questionHandler.HealthCheck)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 图片分析接口
		api.POST("/analyze-image", questionHandler.AnalyzeImage)
	}

	// 根路径返回API信息
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "Solve API Go",
			"version": "1.0.0",
			"description": "图片题目解析API服务",
			"endpoints": gin.H{
				"health": "/health",
				"analyze": "/api/v1/analyze-image",
			},
		})
	})
}
