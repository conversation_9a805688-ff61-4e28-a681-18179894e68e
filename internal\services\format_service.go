package services

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"solve_api_go/internal/models"
)

// FormatService 数据格式化服务
type FormatService struct{}

// NewFormatService 创建新的数据格式化服务实例
func NewFormatService() *FormatService {
	return &FormatService{}
}

// FormatQwenData 将Qwen返回的数据进行格式化处理
// 这是核心的数据预处理方法，负责清洗和验证Qwen返回的数据
func (s *FormatService) FormatQwenData(qwenRawData string) (*models.QwenResponse, error) {
	// 解析JSON数据
	var qwenResp models.QwenResponse
	if err := json.Unmarshal([]byte(qwenRawData), &qwenResp); err != nil {
		return nil, fmt.Errorf("解析Qwen数据失败: %v", err)
	}

	// 验证题目类型
	if err := s.validateQuestionType(qwenResp.QuestionType); err != nil {
		return nil, err
	}

	// 清洗题目文本
	qwenResp.QuestionText = s.cleanQuestionText(qwenResp.QuestionText)

	// 验证选项完整性
	if err := s.validateOptions(&qwenResp); err != nil {
		return nil, err
	}

	return &qwenResp, nil
}

// validateQuestionType 验证题目类型是否有效
func (s *FormatService) validateQuestionType(questionType string) error {
	validTypes := []string{"单选题", "多选题", "判断题"}
	
	// 检查是否为空
	if strings.TrimSpace(questionType) == "" {
		return fmt.Errorf("图片解析异常，请重新拍摄")
	}

	// 检查是否为有效类型
	for _, validType := range validTypes {
		if questionType == validType {
			return nil
		}
	}

	return fmt.Errorf("图片解析异常，请重新拍摄")
}

// cleanQuestionText 清洗题目文本
// 使用正则表达式移除题目前缀，如：(判断题)01、等
func (s *FormatService) cleanQuestionText(questionText string) string {
	// 使用提供的正则表达式清洗题干
	// ^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*
	pattern := `^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*`
	re := regexp.MustCompile(pattern)
	
	// 移除匹配的前缀
	cleanedText := re.ReplaceAllString(questionText, "")
	
	// 去除首尾空白字符
	return strings.TrimSpace(cleanedText)
}

// validateOptions 验证选项的完整性
func (s *FormatService) validateOptions(qwenResp *models.QwenResponse) error {
	switch qwenResp.QuestionType {
	case "判断题":
		// 判断题必须有Y和N选项，且只能有这两个选项
		if qwenResp.Y == "" || qwenResp.N == "" {
			return fmt.Errorf("判断题缺少必要的选项")
		}
		// 清空其他选项
		qwenResp.A = ""
		qwenResp.B = ""
		qwenResp.C = ""
		qwenResp.D = ""
		
	case "单选题", "多选题":
		// 单选题和多选题至少需要A和B选项
		if qwenResp.A == "" || qwenResp.B == "" {
			return fmt.Errorf("选择题至少需要A和B两个选项")
		}
		// 清空判断题选项
		qwenResp.Y = ""
		qwenResp.N = ""
	}

	return nil
}

// GenerateHashData 生成用于哈希的完整数据字符串
// 必须使用完整的格式化后的数据，确保相同内容生成相同哈希
func (s *FormatService) GenerateHashData(qwenResp *models.QwenResponse) string {
	// 构建完整的数据字符串，用于生成哈希
	var builder strings.Builder
	
	builder.WriteString("question_type:")
	builder.WriteString(qwenResp.QuestionType)
	builder.WriteString("|question_text:")
	builder.WriteString(qwenResp.QuestionText)
	
	// 按固定顺序添加选项，确保哈希一致性
	if qwenResp.A != "" {
		builder.WriteString("|option_a:")
		builder.WriteString(qwenResp.A)
	}
	if qwenResp.B != "" {
		builder.WriteString("|option_b:")
		builder.WriteString(qwenResp.B)
	}
	if qwenResp.C != "" {
		builder.WriteString("|option_c:")
		builder.WriteString(qwenResp.C)
	}
	if qwenResp.D != "" {
		builder.WriteString("|option_d:")
		builder.WriteString(qwenResp.D)
	}
	if qwenResp.Y != "" {
		builder.WriteString("|option_y:")
		builder.WriteString(qwenResp.Y)
	}
	if qwenResp.N != "" {
		builder.WriteString("|option_n:")
		builder.WriteString(qwenResp.N)
	}
	
	return builder.String()
}

// ConvertToJSON 将QwenResponse转换为JSON字符串
func (s *FormatService) ConvertToJSON(qwenResp *models.QwenResponse) (string, error) {
	jsonData, err := json.Marshal(qwenResp)
	if err != nil {
		return "", fmt.Errorf("转换为JSON失败: %v", err)
	}
	return string(jsonData), nil
}
