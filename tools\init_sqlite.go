package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"

	_ "modernc.org/sqlite"
)

func main() {
	fmt.Println("=== SQLite数据库初始化工具 ===")
	fmt.Println()

	dbPath := "solve_api_go.db"
	
	// 连接SQLite数据库（如果不存在会自动创建）
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		log.Fatalf("连接SQLite数据库失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		log.Fatalf("SQLite数据库连接测试失败: %v", err)
	}

	fmt.Printf("✅ SQLite数据库连接成功: %s\n", dbPath)

	// 执行迁移脚本
	if err := runSQLiteMigrations(db); err != nil {
		log.Fatalf("执行迁移脚本失败: %v", err)
	}

	fmt.Println()
	fmt.Println("🎉 SQLite数据库初始化完成!")
	fmt.Printf("数据库文件: %s\n", dbPath)
}

func runSQLiteMigrations(db *sql.DB) error {
	fmt.Println()
	fmt.Println("开始执行数据库迁移脚本...")

	// 创建SQLite版本的迁移脚本
	migrations := []struct {
		name string
		sql  string
	}{
		{
			name: "001_create_questions_table",
			sql: `
CREATE TABLE IF NOT EXISTS questions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  cache_key_hash TEXT NOT NULL,
  question_type TEXT NOT NULL,
  question_text TEXT NOT NULL,
  option_a TEXT,
  option_b TEXT,
  option_c TEXT,
  option_d TEXT,
  option_y TEXT,
  option_n TEXT,
  answer TEXT,
  analysis TEXT,
  user_image TEXT,
  image_url TEXT NOT NULL,
  qwen_raw TEXT,
  deepseek_raw TEXT,
  qwen_parsed TEXT,
  is_verified INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_cache_key_hash ON questions(cache_key_hash);
CREATE INDEX IF NOT EXISTS idx_question_type ON questions(question_type);
CREATE INDEX IF NOT EXISTS idx_created_at ON questions(created_at);
			`,
		},
		{
			name: "002_create_model_config_table",
			sql: `
CREATE TABLE IF NOT EXISTS quest_model_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  model_name TEXT NOT NULL UNIQUE,
  role_system TEXT NOT NULL,
  role_user TEXT NOT NULL,
  temperature REAL NOT NULL DEFAULT 0.0,
  top_p REAL NOT NULL DEFAULT 0.8,
  top_k INTEGER NOT NULL DEFAULT 50,
  repetition_penalty REAL NOT NULL DEFAULT 1.0,
  presence_penalty REAL NOT NULL DEFAULT 1.5,
  response_format TEXT NOT NULL DEFAULT 'json_object',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入Qwen模型默认配置
INSERT OR REPLACE INTO quest_model_config (
  model_name, 
  role_system, 
  role_user, 
  temperature, 
  top_p, 
  top_k, 
  repetition_penalty, 
  presence_penalty, 
  response_format
) VALUES (
  'qwen-vl-plus',
  '你是一个专业的题目识别助手，能够准确识别图片中的题目内容并按照指定格式返回。',
  '请识别图片中的题目内容，包括题目类型、题目文本和所有选项，并按照JSON格式返回。',
  0.0,
  0.8,
  50,
  1.0,
  1.5,
  'json_object'
);

-- 插入DeepSeek模型默认配置
INSERT OR REPLACE INTO quest_model_config (
  model_name, 
  role_system, 
  role_user, 
  temperature, 
  top_p, 
  top_k, 
  repetition_penalty, 
  presence_penalty, 
  response_format
) VALUES (
  'deepseek-chat',
  '你是一个专业的题目分析助手，能够根据题目内容提供准确的答案和详细的解析。',
  '请根据提供的题目内容，分析并返回正确答案和详细解析，按照JSON格式返回。',
  1.0,
  1.0,
  50,
  0.0,
  0.0,
  'json_object'
);
			`,
		},
	}

	// 执行每个迁移
	for _, migration := range migrations {
		fmt.Printf("执行迁移脚本: %s\n", migration.name)
		
		// 分割SQL语句（以分号分隔）
		statements := strings.Split(migration.sql, ";")
		
		for _, stmt := range statements {
			stmt = strings.TrimSpace(stmt)
			if stmt == "" || strings.HasPrefix(stmt, "--") {
				continue
			}

			_, err := db.Exec(stmt)
			if err != nil {
				return fmt.Errorf("执行SQL语句失败: %v\nSQL: %s", err, stmt)
			}
		}

		fmt.Printf("  ✅ %s 执行成功\n", migration.name)
	}

	return nil
}

func init() {
	// 确保在正确的目录中运行
	if _, err := os.Stat("go.mod"); os.IsNotExist(err) {
		fmt.Println("错误: 请在项目根目录中运行此工具")
		os.Exit(1)
	}
}
