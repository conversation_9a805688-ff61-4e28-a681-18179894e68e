-- 创建AI模型配置表
-- 存储AI模型的动态配置参数，支持Qwen和DeepSeek模型的参数配置

CREATE TABLE IF NOT EXISTS `quest_model_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(50) NOT NULL COMMENT '模型名称，如qwen-vl-plus, deepseek-chat',
  `role_system` text NOT NULL COMMENT 'system角色的Content',
  `role_user` text NOT NULL COMMENT 'user角色的Content',
  `temperature` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '温度，默认值为0',
  `top_p` decimal(3,2) NOT NULL DEFAULT '0.80' COMMENT 'TopP，默认值为0.8，越高文本越多样性',
  `top_k` int NOT NULL DEFAULT '50' COMMENT 'TopK，默认值为50，候选集越少，稳定性越高',
  `repetition_penalty` decimal(4,3) NOT NULL DEFAULT '1.000' COMMENT '重复惩罚，OCR建议为1-1.05',
  `presence_penalty` decimal(3,2) NOT NULL DEFAULT '1.50' COMMENT '存在惩罚，OCR建议1.5',
  `response_format` varchar(20) NOT NULL DEFAULT 'json_object' COMMENT '返回格式，json_object或text',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name` (`model_name`) COMMENT '模型名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表';

-- 插入Qwen模型默认配置
INSERT INTO `quest_model_config` (
  `model_name`, 
  `role_system`, 
  `role_user`, 
  `temperature`, 
  `top_p`, 
  `top_k`, 
  `repetition_penalty`, 
  `presence_penalty`, 
  `response_format`
) VALUES (
  'qwen-vl-plus',
  '你是一个专业的题目识别助手，能够准确识别图片中的题目内容并按照指定格式返回。',
  '请识别图片中的题目内容，包括题目类型、题目文本和所有选项，并按照JSON格式返回。',
  0.00,
  0.80,
  50,
  1.000,
  1.50,
  'json_object'
) ON DUPLICATE KEY UPDATE
  `role_system` = VALUES(`role_system`),
  `role_user` = VALUES(`role_user`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 插入DeepSeek模型默认配置
INSERT INTO `quest_model_config` (
  `model_name`, 
  `role_system`, 
  `role_user`, 
  `temperature`, 
  `top_p`, 
  `top_k`, 
  `repetition_penalty`, 
  `presence_penalty`, 
  `response_format`
) VALUES (
  'deepseek-chat',
  '你是一个专业的题目分析助手，能够根据题目内容提供准确的答案和详细的解析。',
  '请根据提供的题目内容，分析并返回正确答案和详细解析，按照JSON格式返回。',
  1.00,
  1.00,
  50,
  0.000,
  0.00,
  'json_object'
) ON DUPLICATE KEY UPDATE
  `role_system` = VALUES(`role_system`),
  `role_user` = VALUES(`role_user`),
  `updated_at` = CURRENT_TIMESTAMP;
