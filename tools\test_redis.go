package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	"solve_api_go/internal/config"
)

func main() {
	fmt.Println("=== Redis连接调试工具 ===")
	fmt.Println()

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("Redis配置:\n")
	fmt.Printf("  Host: %s\n", cfg.Redis.Host)
	fmt.Printf("  Port: %s\n", cfg.Redis.Port)
	fmt.Printf("  Username: %s\n", cfg.Redis.Username)
	fmt.Printf("  Password: %s\n", cfg.Redis.Password)
	fmt.Printf("  DB: %d\n", cfg.Redis.DB)
	fmt.Printf("  Address: %s\n", cfg.Redis.GetRedisAddr())
	fmt.Println()

	// 尝试不同的连接方式
	fmt.Println("1. 尝试使用配置的用户名和密码连接...")
	testRedisConnectionWithAuth(cfg.Redis.GetRedisAddr(), cfg.Redis.Username, cfg.Redis.Password, cfg.Redis.DB)

	fmt.Println()
	fmt.Println("2. 尝试不使用用户名连接...")
	testRedisConnectionWithAuth(cfg.Redis.GetRedisAddr(), "", cfg.Redis.Password, cfg.Redis.DB)
}

func testRedisConnectionWithAuth(addr, username, password string, db int) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,
		Username: username,
		Password: password,
		DB:       db,
		PoolSize: 1,
	})
	defer rdb.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 连接成功: %s\n", pong)

	// 测试基本操作
	testKey := "test:debug"
	err = rdb.Set(ctx, testKey, "debug_value", time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ 写入测试失败: %v\n", err)
		return
	}

	val, err := rdb.Get(ctx, testKey).Result()
	if err != nil {
		fmt.Printf("❌ 读取测试失败: %v\n", err)
		return
	}

	if val == "debug_value" {
		fmt.Println("✅ 读写测试成功")
	} else {
		fmt.Printf("❌ 读写测试失败: 期望 'debug_value', 得到 '%s'\n", val)
	}

	// 清理测试数据
	rdb.Del(ctx, testKey)
}
