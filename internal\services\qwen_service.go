package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"solve_api_go/internal/config"
	"solve_api_go/internal/models"
)

// QwenService Qwen AI服务
type QwenService struct {
	apiKey string
	client *http.Client
	dbService *DatabaseService
}

// NewQwenService 创建新的Qwen AI服务实例
func NewQwenService(cfg *config.Config, dbService *DatabaseService) *QwenService {
	return &QwenService{
		apiKey: cfg.AI.QwenKey,
		client: &http.Client{
			Timeout: 60 * time.Second, // 设置60秒超时
		},
		dbService: dbService,
	}
}

// QwenAPIResponse Qwen API响应结构体
type QwenAPIResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// AnalyzeImage 使用Qwen模型分析图片
func (s *QwenService) AnalyzeImage(imageURL string) (string, error) {
	// 获取Qwen模型配置
	modelConfig, err := s.dbService.GetModelConfig("qwen-vl-plus")
	if err != nil {
		return "", fmt.Errorf("获取Qwen模型配置失败: %v", err)
	}

	// 构建请求
	qwenConfig := modelConfig.ToQwenConfig()
	request := qwenConfig.BuildQwenRequest(imageURL)

	// 序列化请求数据
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	bodyText, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("Qwen API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(bodyText))
	}

	// 解析响应
	var apiResp QwenAPIResponse
	if err := json.Unmarshal(bodyText, &apiResp); err != nil {
		return "", fmt.Errorf("解析Qwen响应失败: %v", err)
	}

	// 检查响应内容
	if len(apiResp.Output.Choices) == 0 {
		return "", fmt.Errorf("Qwen响应中没有内容")
	}

	content := apiResp.Output.Choices[0].Message.Content
	if content == "" {
		return "", fmt.Errorf("Qwen响应内容为空")
	}

	return content, nil
}

// ParseQwenResponse 解析Qwen响应内容
func (s *QwenService) ParseQwenResponse(content string) (*models.QwenResponse, error) {
	var qwenResp models.QwenResponse
	if err := json.Unmarshal([]byte(content), &qwenResp); err != nil {
		return nil, fmt.Errorf("解析Qwen响应内容失败: %v", err)
	}
	return &qwenResp, nil
}
